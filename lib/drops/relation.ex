defmodule Drops.Relation do
  defmacro __using__(opts) do
    quote do
      use Ecto.Schema

      import Drops.Relation

      @before_compile Drops.Relation

      @opts unquote(opts)
    end
  end

  defmacro __before_compile__(env) do
    relation = env.module

    opts = Module.get_attribute(relation, :opts)
    repo = opts[:repo]
    name = opts[:name]

    schema = Drops.Relation.Inference.infer_schema(relation, name, repo)

    quote do
      require unquote(repo)

      unquote(schema)
    end
  end

  defmodule Inference do
    def infer_schema(relation, name, repo) do
      # TODO: introspect table columns and types
      columns = repo.query!("TODO")

      quote do
        # generate ecto schema from columns info
      end
    end
  end
end
